# 简易FTP服务器实现

本项目在现有网络协议栈基础上实现了一个简易的FTP服务器，支持基本的文件上传和下载功能。

## 功能特性

### 支持的FTP命令
- **USER** - 用户名认证
- **PASS** - 密码认证
- **LIST** - 列出目录内容
- **RETR** - 下载文件
- **STOR** - 上传文件
- **PWD** - 显示当前目录
- **CWD** - 改变目录
- **QUIT** - 退出连接
- **PASV** - 被动模式
- **TYPE** - 设置传输类型（ASCII/Binary）
- **SYST** - 系统信息
- **FEAT** - 功能列表

### 用户认证
- **anonymous** - 匿名用户（无需密码）
- **ftp** - 匿名用户（无需密码）
- **admin/admin** - 管理员用户

### 文件管理
- 支持文件上传和下载
- 文件权限检查
- 目录浏览
- 文件完整性保证

## 编译和运行

### 编译
```bash
cd build
cmake --build . --target ftp_server
```

### 运行
```bash
./ftp_server.exe
```

服务器将在端口21上启动，FTP根目录为 `./ftp_root`

## 测试

### 使用Python测试脚本
```bash
python test_ftp.py
```

### 使用标准FTP客户端
```bash
ftp localhost
# 或者
ftp 127.0.0.1
```

然后使用以下命令：
```
User: anonymous
ftp> ls
ftp> get readme.txt
ftp> put myfile.txt
ftp> pwd
ftp> cd subdirectory
ftp> quit
```

## 协议实现细节

### 控制连接
- 端口：21
- 用于传输FTP命令和响应
- 基于TCP协议

### 数据连接
- 被动模式：服务器选择随机端口（20000-29999）
- 用于传输文件数据和目录列表
- 基于TCP协议

### 响应码
- 220 - 服务就绪
- 221 - 再见
- 226 - 数据连接关闭，传输完成
- 227 - 进入被动模式
- 230 - 登录成功
- 250 - 文件操作成功
- 331 - 需要密码
- 500 - 语法错误
- 530 - 未登录
- 550 - 文件未找到

## 文件结构

```
include/ftp.h       - FTP协议头文件
src/ftp.c          - FTP协议实现
app/ftp_server.c   - FTP服务器应用
ftp_root/          - FTP根目录
  ├── readme.txt   - 测试文件
  └── test.txt     - 测试文件
```

## 安全考虑

- 简单的用户认证机制
- 文件访问权限检查
- 路径安全验证
- 连接数限制

## 限制

- 仅支持被动模式（PASV）
- 最大并发连接数：10
- 简化的用户认证
- 基本的错误处理

## 扩展建议

1. 添加主动模式支持
2. 实现更安全的用户认证
3. 添加SSL/TLS支持
4. 实现断点续传
5. 添加日志记录功能
