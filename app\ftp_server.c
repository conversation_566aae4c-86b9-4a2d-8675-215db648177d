#include "driver.h"
#include "net.h"
#include "ftp.h"

#ifdef TCP
#include "tcp.h"

int main(int argc, char const *argv[]) {
    printf("=== Simple FTP Server ===\n");
    printf("Starting FTP server...\n");
    
    // 初始化协议栈
    if (net_init() == -1) {
        printf("Network initialization failed.\n");
        return -1;
    }
    printf("Network stack initialized.\n");
    
    // 初始化FTP服务器
    ftp_init();
    
    // 启动FTP服务器
    ftp_server_start();
    
    printf("FTP server is running on port %d\n", FTP_CONTROL_PORT);
    printf("Root directory: %s\n", FTP_ROOT_DIR);
    printf("Supported commands: USER, PASS, LIST, RETR, STOR, PWD, CWD, QUIT, PASV, TYPE, SYST, FEAT\n");
    printf("Default users:\n");
    printf("  - anonymous (no password required)\n");
    printf("  - admin/admin\n");
    printf("\nPress Ctrl+C to stop the server.\n\n");
    
    // 主循环
    while (1) {
        net_poll();  // 处理网络事件
    }
    
    return 0;
}

#else
int main(int argc, char const *argv[]) {
    printf("TCP support is not enabled. Please compile with TCP=1.\n");
    return -1;
}
#endif
