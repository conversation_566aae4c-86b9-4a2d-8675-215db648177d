#ifndef FTP_H
#define FTP_H

#include "net.h"
#include "tcp.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

// FTP端口定义
#define FTP_CONTROL_PORT 21
#define FTP_DATA_PORT 20

// FTP响应码
#define FTP_READY 220
#define FTP_GOODBYE 221
#define FTP_DATA_CONNECTION_OPEN 225
#define FTP_CLOSING_DATA_CONNECTION 226
#define FTP_PASSIVE_MODE 227
#define FTP_LOGIN_SUCCESS 230
#define FTP_FILE_ACTION_OK 250
#define FTP_PATHNAME_CREATED 257
#define FTP_USER_OK_NEED_PASS 331
#define FTP_NEED_ACCOUNT 332
#define FTP_FILE_PENDING 350
#define FTP_SERVICE_NOT_AVAILABLE 421
#define FTP_CANNOT_OPEN_DATA_CONNECTION 425
#define FTP_CONNECTION_CLOSED 426
#define FTP_FILE_ACTION_NOT_TAKEN 450
#define FTP_ACTION_ABORTED 451
#define FTP_INSUFFICIENT_STORAGE 452
#define FTP_SYNTAX_ERROR 500
#define FTP_SYNTAX_ERROR_PARAMETERS 501
#define FTP_COMMAND_NOT_IMPLEMENTED 502
#define FTP_BAD_SEQUENCE 503
#define FTP_PARAMETER_NOT_IMPLEMENTED 504
#define FTP_NOT_LOGGED_IN 530
#define FTP_NEED_ACCOUNT_FOR_STORING 532
#define FTP_FILE_NOT_FOUND 550
#define FTP_PAGE_TYPE_UNKNOWN 551
#define FTP_EXCEEDED_STORAGE 552
#define FTP_FILE_NAME_NOT_ALLOWED 553

// FTP命令类型
typedef enum {
    FTP_CMD_USER,
    FTP_CMD_PASS,
    FTP_CMD_LIST,
    FTP_CMD_RETR,
    FTP_CMD_STOR,
    FTP_CMD_PWD,
    FTP_CMD_CWD,
    FTP_CMD_QUIT,
    FTP_CMD_PASV,
    FTP_CMD_TYPE,
    FTP_CMD_SYST,
    FTP_CMD_FEAT,
    FTP_CMD_UNKNOWN
} ftp_cmd_t;

// FTP连接状态
typedef enum {
    FTP_STATE_WAIT_USER,
    FTP_STATE_WAIT_PASS,
    FTP_STATE_LOGGED_IN,
    FTP_STATE_DATA_TRANSFER
} ftp_state_t;

// FTP传输模式
typedef enum {
    FTP_MODE_ACTIVE,
    FTP_MODE_PASSIVE
} ftp_mode_t;

// FTP传输类型
typedef enum {
    FTP_TYPE_ASCII,
    FTP_TYPE_BINARY
} ftp_type_t;

// FTP会话结构
typedef struct ftp_session {
    ftp_state_t state;
    ftp_mode_t mode;
    ftp_type_t type;
    char username[64];
    char current_dir[256];
    uint8_t client_ip[NET_IP_LEN];
    uint16_t client_port;
    uint16_t data_port;
    tcp_conn_t *control_conn;
    tcp_conn_t *data_conn;
    FILE *transfer_file;
    int authenticated;
} ftp_session_t;

// FTP命令结构
typedef struct ftp_command {
    ftp_cmd_t cmd;
    char arg[256];
} ftp_command_t;

// 最大并发连接数
#define FTP_MAX_CONNECTIONS 10

// 默认FTP根目录
#define FTP_ROOT_DIR "./ftp_root"

// 函数声明
void ftp_init();
void ftp_server_start();
void ftp_control_handler(tcp_conn_t *tcp_conn, uint8_t *data, size_t len, uint8_t *src_ip, uint16_t src_port);
void ftp_data_handler(tcp_conn_t *tcp_conn, uint8_t *data, size_t len, uint8_t *src_ip, uint16_t src_port);

// FTP命令处理函数
ftp_command_t ftp_parse_command(const char *cmd_line);
void ftp_send_response(tcp_conn_t *conn, uint8_t *dst_ip, uint16_t dst_port, int code, const char *message);
void ftp_handle_user(ftp_session_t *session, const char *username);
void ftp_handle_pass(ftp_session_t *session, const char *password);
void ftp_handle_list(ftp_session_t *session);
void ftp_handle_retr(ftp_session_t *session, const char *filename);
void ftp_handle_stor(ftp_session_t *session, const char *filename);
void ftp_handle_pwd(ftp_session_t *session);
void ftp_handle_cwd(ftp_session_t *session, const char *path);
void ftp_handle_quit(ftp_session_t *session);
void ftp_handle_pasv(ftp_session_t *session);
void ftp_handle_type(ftp_session_t *session, const char *type);
void ftp_handle_syst(ftp_session_t *session);
void ftp_handle_feat(ftp_session_t *session);

// 工具函数
ftp_session_t* ftp_get_session(uint8_t *client_ip, uint16_t client_port);
ftp_session_t* ftp_create_session(uint8_t *client_ip, uint16_t client_port, tcp_conn_t *conn);
void ftp_destroy_session(ftp_session_t *session);
int ftp_authenticate(const char *username, const char *password);
int ftp_check_file_permission(const char *filepath, int write_access);
void ftp_get_file_list(const char *dir_path, char *buffer, size_t buffer_size);
char* ftp_get_absolute_path(const char *current_dir, const char *relative_path);

#endif // FTP_H
