/*
 * 简单的FTP连接测试程序
 * 测试是否能连接到FTP服务器
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
    #define close closesocket
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <unistd.h>
#endif

#define FTP_SERVER_IP "**************"
#define FTP_SERVER_PORT 21

int main() {
    int sockfd;
    struct sockaddr_in server_addr;
    char buffer[1024];
    int result;
    
    printf("=== 简单FTP连接测试 ===\n");
    printf("目标服务器: %s:%d\n", FTP_SERVER_IP, FTP_SERVER_PORT);
    
#ifdef _WIN32
    // 初始化Winsock
    WSADATA wsaData;
    result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        printf("WSAStartup失败: %d\n", result);
        return 1;
    }
    printf("Winsock初始化成功\n");
#endif
    
    // 创建socket
    sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd < 0) {
        printf("创建socket失败\n");
        goto cleanup;
    }
    printf("Socket创建成功\n");
    
    // 设置服务器地址
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(FTP_SERVER_PORT);
    
    if (inet_pton(AF_INET, FTP_SERVER_IP, &server_addr.sin_addr) <= 0) {
        printf("IP地址转换失败\n");
        goto cleanup;
    }
    printf("服务器地址设置成功\n");
    
    // 尝试连接
    printf("正在连接到服务器...\n");
    result = connect(sockfd, (struct sockaddr*)&server_addr, sizeof(server_addr));
    if (result < 0) {
        printf("连接失败\n");
        printf("可能的原因:\n");
        printf("1. FTP服务器未运行\n");
        printf("2. IP地址或端口错误\n");
        printf("3. 防火墙阻止连接\n");
        printf("4. 自定义协议栈不接受标准TCP连接\n");
        goto cleanup;
    }
    
    printf("✅ 连接成功建立!\n");
    
    // 尝试接收欢迎消息
    printf("等待服务器响应...\n");
    result = recv(sockfd, buffer, sizeof(buffer) - 1, 0);
    if (result > 0) {
        buffer[result] = '\0';
        printf("✅ 收到服务器响应: %s\n", buffer);
    } else {
        printf("⚠️ 未收到服务器响应\n");
    }
    
    // 发送USER命令测试
    printf("发送USER命令...\n");
    const char* user_cmd = "USER anonymous\r\n";
    result = send(sockfd, user_cmd, strlen(user_cmd), 0);
    if (result > 0) {
        printf("✅ USER命令发送成功\n");
        
        // 接收响应
        result = recv(sockfd, buffer, sizeof(buffer) - 1, 0);
        if (result > 0) {
            buffer[result] = '\0';
            printf("✅ 收到响应: %s\n", buffer);
        }
    } else {
        printf("❌ USER命令发送失败\n");
    }
    
cleanup:
    if (sockfd >= 0) {
        close(sockfd);
        printf("连接已关闭\n");
    }
    
#ifdef _WIN32
    WSACleanup();
#endif
    
    printf("测试完成\n");
    return 0;
}
