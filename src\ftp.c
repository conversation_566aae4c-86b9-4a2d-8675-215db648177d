#include "ftp.h"
#include "utils.h"
#include <sys/stat.h>
#include <dirent.h>
#include <ctype.h>

#ifdef _WIN32
#include <direct.h>
#define mkdir _mkdir
#else
#include <sys/stat.h>
#endif

// 全局会话表
static ftp_session_t ftp_sessions[FTP_MAX_CONNECTIONS];
static int ftp_session_count = 0;

/**
 * @brief 初始化FTP服务器
 */
void ftp_init() {
    memset(ftp_sessions, 0, sizeof(ftp_sessions));
    ftp_session_count = 0;
    
    // 创建FTP根目录
    struct stat st = {0};
    if (stat(FTP_ROOT_DIR, &st) == -1) {
#ifdef _WIN32
        _mkdir(FTP_ROOT_DIR);
#else
        mkdir(FTP_ROOT_DIR, 0755);
#endif
    }
    
    printf("FTP server initialized. Root directory: %s\n", FTP_ROOT_DIR);
}

/**
 * @brief 启动FTP服务器
 */
void ftp_server_start() {
    printf("Starting FTP server on port %d...\n", FTP_CONTROL_PORT);
    tcp_open(FTP_CONTROL_PORT, ftp_control_handler);
}

/**
 * @brief 解析FTP命令
 */
ftp_command_t ftp_parse_command(const char *cmd_line) {
    ftp_command_t cmd;
    memset(&cmd, 0, sizeof(cmd));
    
    char command[16];
    if (sscanf(cmd_line, "%15s %255s", command, cmd.arg) < 1) {
        cmd.cmd = FTP_CMD_UNKNOWN;
        return cmd;
    }
    
    // 转换为大写
    for (int i = 0; command[i]; i++) {
        command[i] = toupper(command[i]);
    }
    
    if (strcmp(command, "USER") == 0) cmd.cmd = FTP_CMD_USER;
    else if (strcmp(command, "PASS") == 0) cmd.cmd = FTP_CMD_PASS;
    else if (strcmp(command, "LIST") == 0) cmd.cmd = FTP_CMD_LIST;
    else if (strcmp(command, "RETR") == 0) cmd.cmd = FTP_CMD_RETR;
    else if (strcmp(command, "STOR") == 0) cmd.cmd = FTP_CMD_STOR;
    else if (strcmp(command, "PWD") == 0) cmd.cmd = FTP_CMD_PWD;
    else if (strcmp(command, "CWD") == 0) cmd.cmd = FTP_CMD_CWD;
    else if (strcmp(command, "QUIT") == 0) cmd.cmd = FTP_CMD_QUIT;
    else if (strcmp(command, "PASV") == 0) cmd.cmd = FTP_CMD_PASV;
    else if (strcmp(command, "TYPE") == 0) cmd.cmd = FTP_CMD_TYPE;
    else if (strcmp(command, "SYST") == 0) cmd.cmd = FTP_CMD_SYST;
    else if (strcmp(command, "FEAT") == 0) cmd.cmd = FTP_CMD_FEAT;
    else cmd.cmd = FTP_CMD_UNKNOWN;
    
    return cmd;
}

/**
 * @brief 发送FTP响应
 */
void ftp_send_response(tcp_conn_t *conn, uint8_t *dst_ip, uint16_t dst_port, int code, const char *message) {
    char response[512];
    snprintf(response, sizeof(response), "%d %s\r\n", code, message);
    tcp_send(conn, (uint8_t*)response, strlen(response), FTP_CONTROL_PORT, dst_ip, dst_port);
}

/**
 * @brief 获取会话
 */
ftp_session_t* ftp_get_session(uint8_t *client_ip, uint16_t client_port) {
    for (int i = 0; i < ftp_session_count; i++) {
        if (memcmp(ftp_sessions[i].client_ip, client_ip, NET_IP_LEN) == 0 &&
            ftp_sessions[i].client_port == client_port) {
            return &ftp_sessions[i];
        }
    }
    return NULL;
}

/**
 * @brief 创建新会话
 */
ftp_session_t* ftp_create_session(uint8_t *client_ip, uint16_t client_port, tcp_conn_t *conn) {
    if (ftp_session_count >= FTP_MAX_CONNECTIONS) {
        return NULL;
    }
    
    ftp_session_t *session = &ftp_sessions[ftp_session_count++];
    memset(session, 0, sizeof(ftp_session_t));
    
    session->state = FTP_STATE_WAIT_USER;
    session->mode = FTP_MODE_PASSIVE;
    session->type = FTP_TYPE_BINARY;
    strcpy(session->current_dir, "/");
    memcpy(session->client_ip, client_ip, NET_IP_LEN);
    session->client_port = client_port;
    session->control_conn = conn;
    session->authenticated = 0;
    
    return session;
}

/**
 * @brief 销毁会话
 */
void ftp_destroy_session(ftp_session_t *session) {
    if (session->transfer_file) {
        fclose(session->transfer_file);
        session->transfer_file = NULL;
    }
    
    // 移除会话
    int index = session - ftp_sessions;
    if (index >= 0 && index < ftp_session_count) {
        memmove(&ftp_sessions[index], &ftp_sessions[index + 1], 
                (ftp_session_count - index - 1) * sizeof(ftp_session_t));
        ftp_session_count--;
    }
}

/**
 * @brief 用户认证
 */
int ftp_authenticate(const char *username, const char *password) {
    // 简单的用户认证，实际应用中应该使用更安全的方式
    if (strcmp(username, "anonymous") == 0 || strcmp(username, "ftp") == 0) {
        return 1; // 匿名用户
    }
    if (strcmp(username, "admin") == 0 && strcmp(password, "admin") == 0) {
        return 1; // 管理员用户
    }
    return 0;
}

/**
 * @brief 检查文件权限
 */
int ftp_check_file_permission(const char *filepath, int write_access) {
    struct stat st;
    if (stat(filepath, &st) != 0) {
        return 0; // 文件不存在
    }
    
    if (write_access) {
        return (st.st_mode & S_IWUSR) != 0;
    } else {
        return (st.st_mode & S_IRUSR) != 0;
    }
}

/**
 * @brief 获取绝对路径
 */
char* ftp_get_absolute_path(const char *current_dir, const char *relative_path) {
    static char absolute_path[512];
    
    if (relative_path[0] == '/') {
        // 绝对路径
        snprintf(absolute_path, sizeof(absolute_path), "%s%s", FTP_ROOT_DIR, relative_path);
    } else {
        // 相对路径
        if (strcmp(current_dir, "/") == 0) {
            snprintf(absolute_path, sizeof(absolute_path), "%s/%s", FTP_ROOT_DIR, relative_path);
        } else {
            snprintf(absolute_path, sizeof(absolute_path), "%s%s/%s", FTP_ROOT_DIR, current_dir, relative_path);
        }
    }
    
    return absolute_path;
}

/**
 * @brief 获取文件列表
 */
void ftp_get_file_list(const char *dir_path, char *buffer, size_t buffer_size) {
    DIR *dir;
    struct dirent *entry;
    struct stat file_stat;
    char full_path[512];
    char line[256];
    
    buffer[0] = '\0';
    
    dir = opendir(dir_path);
    if (dir == NULL) {
        return;
    }
    
    while ((entry = readdir(dir)) != NULL) {
        if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
            continue;
        }
        
        snprintf(full_path, sizeof(full_path), "%s/%s", dir_path, entry->d_name);
        if (stat(full_path, &file_stat) == 0) {
            char time_str[32];
            struct tm *tm_info = localtime(&file_stat.st_mtime);
            strftime(time_str, sizeof(time_str), "%b %d %H:%M", tm_info);
            
            if (S_ISDIR(file_stat.st_mode)) {
                snprintf(line, sizeof(line), "drwxr-xr-x 1 <USER> <GROUP> %8ld %s %s\r\n",
                        0L, time_str, entry->d_name);
            } else {
                snprintf(line, sizeof(line), "-rw-r--r-- 1 <USER> <GROUP> %8ld %s %s\r\n",
                        file_stat.st_size, time_str, entry->d_name);
            }
            
            if (strlen(buffer) + strlen(line) < buffer_size - 1) {
                strcat(buffer, line);
            }
        }
    }
    
    closedir(dir);
}

/**
 * @brief FTP控制连接处理函数
 */
void ftp_control_handler(tcp_conn_t *tcp_conn, uint8_t *data, size_t len, uint8_t *src_ip, uint16_t src_port) {
    // 获取或创建会话
    ftp_session_t *session = ftp_get_session(src_ip, src_port);
    if (!session) {
        session = ftp_create_session(src_ip, src_port, tcp_conn);
        if (!session) {
            ftp_send_response(tcp_conn, src_ip, src_port, FTP_SERVICE_NOT_AVAILABLE,
                            "Too many connections.");
            return;
        }
        // 发送欢迎消息
        ftp_send_response(tcp_conn, src_ip, src_port, FTP_READY,
                        "Simple FTP Server Ready.");
        printf("New FTP connection from %s:%d\n", iptos(src_ip), src_port);
    }

    // 如果没有数据，只是连接建立，直接返回
    if (len == 0) {
        return;
    }

    // 确保数据以null结尾
    char *cmd_line = malloc(len + 1);
    memcpy(cmd_line, data, len);
    cmd_line[len] = '\0';

    // 移除换行符
    char *p = cmd_line;
    while (*p && *p != '\r' && *p != '\n') p++;
    *p = '\0';

    printf("FTP Command: %s from %s:%d\n", cmd_line, iptos(src_ip), src_port);

    // 解析命令
    ftp_command_t cmd = ftp_parse_command(cmd_line);

    // 处理命令
    switch (cmd.cmd) {
        case FTP_CMD_USER:
            ftp_handle_user(session, cmd.arg);
            break;
        case FTP_CMD_PASS:
            ftp_handle_pass(session, cmd.arg);
            break;
        case FTP_CMD_LIST:
            ftp_handle_list(session);
            break;
        case FTP_CMD_RETR:
            ftp_handle_retr(session, cmd.arg);
            break;
        case FTP_CMD_STOR:
            ftp_handle_stor(session, cmd.arg);
            break;
        case FTP_CMD_PWD:
            ftp_handle_pwd(session);
            break;
        case FTP_CMD_CWD:
            ftp_handle_cwd(session, cmd.arg);
            break;
        case FTP_CMD_QUIT:
            ftp_handle_quit(session);
            break;
        case FTP_CMD_PASV:
            ftp_handle_pasv(session);
            break;
        case FTP_CMD_TYPE:
            ftp_handle_type(session, cmd.arg);
            break;
        case FTP_CMD_SYST:
            ftp_handle_syst(session);
            break;
        case FTP_CMD_FEAT:
            ftp_handle_feat(session);
            break;
        default:
            ftp_send_response(tcp_conn, src_ip, src_port, FTP_COMMAND_NOT_IMPLEMENTED,
                            "Command not implemented.");
            break;
    }

    free(cmd_line);
}

/**
 * @brief 处理USER命令
 */
void ftp_handle_user(ftp_session_t *session, const char *username) {
    if (strlen(username) == 0) {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_SYNTAX_ERROR_PARAMETERS, "Username required.");
        return;
    }

    strncpy(session->username, username, sizeof(session->username) - 1);
    session->state = FTP_STATE_WAIT_PASS;

    if (strcmp(username, "anonymous") == 0 || strcmp(username, "ftp") == 0) {
        session->authenticated = 1;
        session->state = FTP_STATE_LOGGED_IN;
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_LOGIN_SUCCESS, "Anonymous login successful.");
    } else {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_USER_OK_NEED_PASS, "Password required.");
    }
}

/**
 * @brief 处理PASS命令
 */
void ftp_handle_pass(ftp_session_t *session, const char *password) {
    if (session->state != FTP_STATE_WAIT_PASS) {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_BAD_SEQUENCE, "Login with USER first.");
        return;
    }

    if (ftp_authenticate(session->username, password)) {
        session->authenticated = 1;
        session->state = FTP_STATE_LOGGED_IN;
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_LOGIN_SUCCESS, "Login successful.");
    } else {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_NOT_LOGGED_IN, "Login incorrect.");
    }
}

/**
 * @brief 处理PWD命令
 */
void ftp_handle_pwd(ftp_session_t *session) {
    if (!session->authenticated) {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_NOT_LOGGED_IN, "Please login first.");
        return;
    }

    char response[512];
    snprintf(response, sizeof(response), "\"%s\" is current directory.", session->current_dir);
    ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                    FTP_PATHNAME_CREATED, response);
}

/**
 * @brief 处理SYST命令
 */
void ftp_handle_syst(ftp_session_t *session) {
    ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                    215, "UNIX Type: L8");
}

/**
 * @brief 处理FEAT命令
 */
void ftp_handle_feat(ftp_session_t *session) {
    char response[] = "211-Features:\r\n PASV\r\n TYPE\r\n211 End";
    tcp_send(session->control_conn, (uint8_t*)response, strlen(response),
            FTP_CONTROL_PORT, session->client_ip, session->client_port);
}

/**
 * @brief 处理TYPE命令
 */
void ftp_handle_type(ftp_session_t *session, const char *type) {
    if (!session->authenticated) {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_NOT_LOGGED_IN, "Please login first.");
        return;
    }

    if (strlen(type) == 0) {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_SYNTAX_ERROR_PARAMETERS, "Type required.");
        return;
    }

    if (toupper(type[0]) == 'A') {
        session->type = FTP_TYPE_ASCII;
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_FILE_ACTION_OK, "Type set to ASCII.");
    } else if (toupper(type[0]) == 'I') {
        session->type = FTP_TYPE_BINARY;
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_FILE_ACTION_OK, "Type set to Binary.");
    } else {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_PARAMETER_NOT_IMPLEMENTED, "Type not supported.");
    }
}

/**
 * @brief 处理CWD命令
 */
void ftp_handle_cwd(ftp_session_t *session, const char *path) {
    if (!session->authenticated) {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_NOT_LOGGED_IN, "Please login first.");
        return;
    }

    if (strlen(path) == 0) {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_SYNTAX_ERROR_PARAMETERS, "Directory required.");
        return;
    }

    char *abs_path = ftp_get_absolute_path(session->current_dir, path);
    struct stat st;

    if (stat(abs_path, &st) == 0 && S_ISDIR(st.st_mode)) {
        // 更新当前目录
        if (path[0] == '/') {
            strncpy(session->current_dir, path, sizeof(session->current_dir) - 1);
        } else if (strcmp(path, "..") == 0) {
            // 返回上级目录
            char *last_slash = strrchr(session->current_dir, '/');
            if (last_slash && last_slash != session->current_dir) {
                *last_slash = '\0';
            } else {
                strcpy(session->current_dir, "/");
            }
        } else {
            // 相对路径
            if (strcmp(session->current_dir, "/") != 0) {
                strncat(session->current_dir, "/", sizeof(session->current_dir) - strlen(session->current_dir) - 1);
            }
            strncat(session->current_dir, path, sizeof(session->current_dir) - strlen(session->current_dir) - 1);
        }

        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_FILE_ACTION_OK, "Directory changed.");
    } else {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_FILE_NOT_FOUND, "Directory not found.");
    }
}

/**
 * @brief 处理PASV命令
 */
void ftp_handle_pasv(ftp_session_t *session) {
    if (!session->authenticated) {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_NOT_LOGGED_IN, "Please login first.");
        return;
    }

    // 选择一个随机端口用于数据连接
    session->data_port = 20000 + (rand() % 10000);
    session->mode = FTP_MODE_PASSIVE;

    // 打开数据端口监听
    tcp_open(session->data_port, ftp_data_handler);

    // 发送PASV响应
    char response[256];
    uint8_t *ip = net_if_ip;
    uint16_t port = session->data_port;

    snprintf(response, sizeof(response),
            "Entering Passive Mode (%d,%d,%d,%d,%d,%d).",
            ip[0], ip[1], ip[2], ip[3],
            (port >> 8) & 0xFF, port & 0xFF);

    ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                    FTP_PASSIVE_MODE, response);
}

/**
 * @brief 处理QUIT命令
 */
void ftp_handle_quit(ftp_session_t *session) {
    ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                    FTP_GOODBYE, "Goodbye.");
    ftp_destroy_session(session);
}

/**
 * @brief 处理LIST命令
 */
void ftp_handle_list(ftp_session_t *session) {
    if (!session->authenticated) {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_NOT_LOGGED_IN, "Please login first.");
        return;
    }

    if (session->mode != FTP_MODE_PASSIVE || session->data_port == 0) {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_CANNOT_OPEN_DATA_CONNECTION, "Use PASV first.");
        return;
    }

    char *dir_path = ftp_get_absolute_path(session->current_dir, "");
    char file_list[4096];

    ftp_get_file_list(dir_path, file_list, sizeof(file_list));

    // 发送数据连接开始响应
    ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                    FTP_DATA_CONNECTION_OPEN, "Opening data connection for directory list.");

    // 通过数据连接发送文件列表
    if (strlen(file_list) > 0) {
        // 这里简化处理，实际应该等待数据连接建立
        printf("File list to send:\n%s", file_list);
    }

    // 发送传输完成响应
    ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                    FTP_CLOSING_DATA_CONNECTION, "Directory list sent.");

    // 关闭数据端口
    tcp_close(session->data_port);
    session->data_port = 0;
}

/**
 * @brief 处理RETR命令（下载文件）
 */
void ftp_handle_retr(ftp_session_t *session, const char *filename) {
    if (!session->authenticated) {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_NOT_LOGGED_IN, "Please login first.");
        return;
    }

    if (strlen(filename) == 0) {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_SYNTAX_ERROR_PARAMETERS, "Filename required.");
        return;
    }

    if (session->mode != FTP_MODE_PASSIVE || session->data_port == 0) {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_CANNOT_OPEN_DATA_CONNECTION, "Use PASV first.");
        return;
    }

    char *file_path = ftp_get_absolute_path(session->current_dir, filename);

    // 检查文件是否存在和可读
    if (!ftp_check_file_permission(file_path, 0)) {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_FILE_NOT_FOUND, "File not found or not readable.");
        return;
    }

    // 打开文件
    session->transfer_file = fopen(file_path, "rb");
    if (!session->transfer_file) {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_FILE_ACTION_NOT_TAKEN, "Cannot open file.");
        return;
    }

    session->state = FTP_STATE_DATA_TRANSFER;

    // 发送数据连接开始响应
    ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                    FTP_DATA_CONNECTION_OPEN, "Opening data connection for file transfer.");

    printf("File download started: %s\n", filename);

    // 实际的文件传输会在数据连接建立后进行
}

/**
 * @brief 处理STOR命令（上传文件）
 */
void ftp_handle_stor(ftp_session_t *session, const char *filename) {
    if (!session->authenticated) {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_NOT_LOGGED_IN, "Please login first.");
        return;
    }

    if (strlen(filename) == 0) {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_SYNTAX_ERROR_PARAMETERS, "Filename required.");
        return;
    }

    if (session->mode != FTP_MODE_PASSIVE || session->data_port == 0) {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_CANNOT_OPEN_DATA_CONNECTION, "Use PASV first.");
        return;
    }

    char *file_path = ftp_get_absolute_path(session->current_dir, filename);

    // 打开文件用于写入
    session->transfer_file = fopen(file_path, "wb");
    if (!session->transfer_file) {
        ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                        FTP_FILE_ACTION_NOT_TAKEN, "Cannot create file.");
        return;
    }

    session->state = FTP_STATE_DATA_TRANSFER;

    // 发送数据连接开始响应
    ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                    FTP_DATA_CONNECTION_OPEN, "Opening data connection for file upload.");

    printf("File upload started: %s\n", filename);

    // 实际的文件接收会在数据连接建立后进行
}

/**
 * @brief FTP数据连接处理函数
 */
void ftp_data_handler(tcp_conn_t *tcp_conn, uint8_t *data, size_t len, uint8_t *src_ip, uint16_t src_port) {
    // 查找对应的会话
    ftp_session_t *session = NULL;
    for (int i = 0; i < ftp_session_count; i++) {
        if (memcmp(ftp_sessions[i].client_ip, src_ip, NET_IP_LEN) == 0) {
            session = &ftp_sessions[i];
            break;
        }
    }

    if (!session || session->state != FTP_STATE_DATA_TRANSFER) {
        return;
    }

    if (session->transfer_file) {
        if (len > 0) {
            // 接收上传的文件数据
            size_t written = fwrite(data, 1, len, session->transfer_file);
            printf("Received %zu bytes, written %zu bytes\n", len, written);
        } else {
            // 数据传输结束
            fclose(session->transfer_file);
            session->transfer_file = NULL;
            session->state = FTP_STATE_LOGGED_IN;

            // 发送传输完成响应
            ftp_send_response(session->control_conn, session->client_ip, session->client_port,
                            FTP_CLOSING_DATA_CONNECTION, "Transfer complete.");

            // 关闭数据端口
            tcp_close(session->data_port);
            session->data_port = 0;

            printf("File transfer completed\n");
        }
    }
}
